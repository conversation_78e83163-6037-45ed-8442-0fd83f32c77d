# AI Agent Prompt: Create Repository Analysis API PRP

## Task Overview
Create a comprehensive Product Requirements Prompt (PRP) for the Repository Analysis API feature. This is the highest priority feature for the CCL project's Phase 2 completion.

## Context
The Repository Analysis API is a core feature of the CCL platform that enables programmatic analysis of code repositories. It's currently 60% complete (research phase done) and needs a full PRP to guide implementation. This API will be the foundation for all other CCL features.

## Files You MUST Read (In This Order)

1. **`PRPs/services/analysis-engine.md`** - Complete service specification
   - Understand the Rust-based architecture
   - Note the performance requirements
   - Review the API endpoint designs

2. **`examples/analysis-engine/ast_parser.rs`** - Example implementation
   - Study the code patterns used
   - Understand the error handling approach
   - Note the async patterns with tokio

3. **`PRPs/implementation-guide.md`** - General patterns
   - Review the Rust-specific guidelines
   - Understand the validation approach
   - Note the testing requirements

4. **`PRPs/api/rest-api.md`** - API design patterns
   - Understand the endpoint structure
   - Review authentication patterns
   - Note the error response formats

5. **`TASK.md`** - Current progress on this feature
   - Review what's been completed (60%)
   - Understand what remains

6. **Context Engineering Example**: Review this structure
   - `.claude/commands/generate-prp.md` - PRP generation process
   - `PRPs/EXAMPLE_multi_agent_prp.md` - Example PRP structure

## Research You MUST Conduct

### 1. Codebase Pattern Research
Search for and document:
- Existing Rust patterns in the examples directory
- Error handling patterns used in the project
- Testing patterns for Rust services
- API endpoint patterns from other services

### 2. External Documentation Research
Research and include references for:
- **Tree-sitter** - For AST parsing (https://tree-sitter.github.io/tree-sitter/)
- **tokio** - For async Rust (https://tokio.rs/)
- **git2-rs** - For Git operations (https://github.com/rust-lang/git2-rs)
- **Language detection libraries** - (e.g., tokei, linguist)

### 3. Performance Optimization Research
Find information about:
- Streaming large files in Rust
- Parallel processing with rayon
- Memory-efficient AST traversal
- Caching strategies for analysis results

## PRP Structure Requirements

Your PRP MUST follow this exact structure:

```markdown
# Repository Analysis API PRP

## Purpose
[2-3 sentences explaining why this feature exists]

## Goal
[Specific, measurable goal statement]

## Business Value
- [Quantifiable benefit 1]
- [Quantifiable benefit 2]
- [Quantifiable benefit 3]

## Success Criteria
- [ ] Analyze 1M LOC in <5 minutes
- [ ] Support 15+ programming languages
- [ ] Handle repositories up to 10GB
- [ ] Achieve 99.9% uptime
- [ ] Maintain <100ms response time for status queries
- [ ] Test coverage >90%

## Documentation

### Internal References
- Service specification: `PRPs/services/analysis-engine.md`
- API patterns: `PRPs/api/rest-api.md`
- Example code: `examples/analysis-engine/ast_parser.rs`
- Implementation guide: `PRPs/implementation-guide.md`

### External Documentation
- Tree-sitter Rust bindings: [URL]
- Tokio async guide: [URL]
- Git2-rs documentation: [URL]
- [Include 5+ more relevant resources]

### Code Examples from Research
```rust
// Include actual code examples you find
```

## Implementation Blueprint

### Phase 1: Core Analysis Engine
```rust
// Pseudocode for main analysis flow
async fn analyze_repository(repo_path: &Path) -> Result<Analysis> {
    // Step 1: Validate repository
    // Step 2: Detect languages
    // Step 3: Parse files in parallel
    // Step 4: Aggregate results
    // Step 5: Generate analysis
}
```

### Phase 2: REST API Layer
[Detailed API implementation steps]

### Phase 3: Performance Optimization
[Optimization strategies]

## Data Models

### Input Models
```rust
pub struct AnalysisRequest {
    pub repository_url: String,
    pub branch: Option<String>,
    pub include_patterns: Vec<String>,
    pub exclude_patterns: Vec<String>,
    pub analysis_depth: AnalysisDepth,
}
```

### Output Models
[Complete data structures]

## API Endpoints

### POST /api/v1/analysis
Initiates repository analysis
[Complete endpoint specification]

### GET /api/v1/analysis/{id}
Retrieves analysis status/results
[Complete endpoint specification]

[Include all endpoints]

## Task List
1. [ ] Set up Rust project structure with cargo workspace
2. [ ] Implement repository cloning with git2-rs
3. [ ] Create language detection module
4. [ ] Implement Tree-sitter parser integration
5. [ ] Build AST analysis engine
6. [ ] Create metrics extraction modules
7. [ ] Implement REST API with Axum/Actix
8. [ ] Add authentication middleware
9. [ ] Create comprehensive test suite
10. [ ] Implement caching layer
11. [ ] Add performance benchmarks
12. [ ] Create API documentation

## Validation Loops

### Syntax & Compilation
```bash
cd analysis-engine
cargo check
cargo clippy -- -D warnings
cargo fmt -- --check
```

### Testing
```bash
cargo test
cargo test --integration
cargo tarpaulin --out Html  # Coverage report
```

### Performance
```bash
cargo bench
./scripts/load-test-analysis.sh
```

### Security
```bash
cargo audit
cargo-deny check
```

## Anti-Patterns to Avoid
- ❌ Loading entire repository into memory
- ❌ Sequential file processing
- ❌ Synchronous Git operations
- ❌ Parsing binary files
- ❌ Ignoring memory limits
- ❌ Not handling large files specially
- ❌ Missing progress indicators

## Known Gotchas
- Tree-sitter grammars must be compiled for target platform
- Git operations can be slow on large repositories
- Language detection can be inaccurate for mixed-language files
- Memory usage scales with repository size
- Some languages require special handling (e.g., Python significant whitespace)

## Performance Considerations
- Use streaming for files >10MB
- Implement progress reporting via WebSocket
- Cache analysis results for 24 hours
- Use connection pooling for Git operations
- Implement circuit breakers for external services

## Confidence Score: X/10
[Provide honest assessment with reasoning]
- High confidence areas: [List]
- Medium confidence areas: [List]
- Low confidence areas: [List]
- Mitigation strategies: [List]
```

## Specific Requirements for This PRP

### Must Include:
1. **Detailed pseudocode** for the core analysis algorithm
2. **Complete API specifications** for all endpoints
3. **Performance benchmarks** with specific targets
4. **Error handling strategy** with custom error types
5. **Progress reporting mechanism** for long-running analyses
6. **Caching strategy** for analysis results
7. **Rate limiting approach** for API endpoints
8. **Language-specific parsers** configuration

### Research to Include:
- Comparison of Tree-sitter vs other parsing approaches
- Benchmarks from similar tools (e.g., GitHub's Linguist)
- Best practices for streaming large files in Rust
- Strategies for handling malformed code

### Validation Commands:
Provide actual commands that can be run to validate the implementation

### Examples to Provide:
- Complete request/response examples for each endpoint
- Error response examples
- Progress notification examples
- Analysis result examples for different repository types

## Quality Criteria

Your PRP will be considered complete when:
- [ ] All sections are filled with specific, actionable content
- [ ] External documentation links are valid and relevant
- [ ] Code examples compile (even if pseudocode, should be logical)
- [ ] Performance targets are measurable and realistic
- [ ] Implementation steps are in correct order
- [ ] All validation commands are executable
- [ ] Confidence score is honest with clear reasoning
- [ ] Anti-patterns section prevents common mistakes
- [ ] The blueprint could be handed to any Rust developer for implementation

## Deliverable
Create file: `PRPs/features/repository-analysis-api.md`

## Additional Notes
- Reference the existing 60% progress in TASK.md
- Ensure compatibility with the overall CCL architecture
- Consider how this API will integrate with other services
- Include considerations for multi-language repository support
- Address security concerns (e.g., analyzing untrusted code)
- Plan for scalability to handle enterprise-scale repositories

Remember: This PRP will guide the actual implementation, so be extremely thorough and specific. Include enough detail that an AI agent could implement this feature with minimal clarification.