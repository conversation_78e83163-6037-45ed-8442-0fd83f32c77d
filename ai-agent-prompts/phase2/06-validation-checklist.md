# AI Agent Prompt: Validate All PRPs for Completeness

## Task Overview
Perform comprehensive validation of all feature PRPs created in Phase 2 to ensure they meet the Context Engineering standards and are ready for Phase 3 execution. This is a quality assurance step to guarantee PRPs are executable.

## Context
You are validating PRPs for the CCL project that were created following the Context Engineering methodology. Each PRP must be complete, accurate, and provide enough detail for an AI agent to implement the feature with minimal clarification.

## Files You MUST Read (In This Order)

1. **All Feature PRPs** (validate each one):
   - `PRPs/features/repository-analysis-api.md`
   - `PRPs/features/natural-language-query.md`
   - `PRPs/features/pattern-detection-mvp.md`
   - `PRPs/features/marketplace-api-foundation.md`

2. **Reference Documents**:
   - `.claude/commands/generate-prp.md` - PRP structure requirements
   - `PRPs/EXAMPLE_multi_agent_prp.md` - Example of complete PRP
   - `finalize-phase2-prp-plan.md` - Expected outcomes

3. **Context Documents**:
   - `CLAUDE.md` - Project conventions
   - `PRPs/implementation-guide.md` - Implementation standards

## Validation Criteria

### Section Completeness Checklist
For EACH PRP, verify ALL sections exist and are complete:

```markdown
## Structural Validation
- [ ] Purpose (2-3 clear sentences)
- [ ] Goal (specific and measurable)
- [ ] Business Value (3+ quantifiable benefits)
- [ ] Success Criteria (5+ measurable items)
- [ ] Documentation (Internal + External references)
- [ ] Implementation Blueprint (detailed pseudocode)
- [ ] Data Models (complete schemas)
- [ ] Task List (10+ ordered tasks)
- [ ] Validation Loops (executable commands)
- [ ] Anti-Patterns (5+ items to avoid)
- [ ] Known Gotchas (platform-specific issues)
- [ ] Performance Considerations
- [ ] Confidence Score (with justification)

## Content Quality Validation
- [ ] External documentation links are valid
- [ ] Code examples are syntactically correct
- [ ] Performance targets are specific
- [ ] API specifications follow standards
- [ ] Database schemas are normalized
- [ ] Security considerations included
- [ ] Error handling specified
- [ ] Integration points clear
```

### Technical Accuracy Validation

#### For Repository Analysis API:
- [ ] Rust code examples compile
- [ ] Tree-sitter integration specified
- [ ] Performance targets achievable
- [ ] API endpoints RESTful
- [ ] Streaming approach for large files

#### For Query Intelligence:
- [ ] Vertex AI configuration correct
- [ ] RAG pipeline complete
- [ ] Handler chain covers query types
- [ ] Caching strategy comprehensive
- [ ] Rate limiting implemented

#### For Pattern Detection:
- [ ] ML algorithms specified
- [ ] Feature extraction detailed
- [ ] BigQuery ML integration clear
- [ ] Training pipeline complete
- [ ] Confidence scoring robust

#### For Marketplace API:
- [ ] Stripe integration accurate
- [ ] API versioning included
- [ ] Search implementation specified
- [ ] Transaction handling correct
- [ ] Security measures comprehensive

## Validation Process

### Step 1: Structural Validation
For each PRP:
1. Check all required sections exist
2. Verify section content meets minimum requirements
3. Ensure logical flow between sections
4. Confirm examples support implementation

### Step 2: Technical Validation
1. Verify code examples are correct for the language
2. Check external documentation links work
3. Validate API designs follow REST/GraphQL standards
4. Ensure database schemas are properly designed
5. Confirm performance targets are realistic

### Step 3: Executability Validation
1. Could an AI agent implement this without clarification?
2. Are validation commands actually executable?
3. Is the implementation blueprint detailed enough?
4. Are all dependencies documented?

### Step 4: Cross-Reference Validation
1. Check consistency with service specifications
2. Verify alignment with implementation guide
3. Ensure compatibility between dependent features
4. Validate shared conventions are followed

## Output Format

Create a validation report with this structure:

```markdown
# Phase 2 PRP Validation Report

## Executive Summary
- Total PRPs Validated: 4
- PRPs Passing Validation: X/4
- PRPs Requiring Updates: X/4
- Overall Readiness: X%

## Individual PRP Validation

### Repository Analysis API PRP
**Status**: ✅ PASSED / ❌ NEEDS REVISION

**Structural Completeness**: X/13 sections complete
- ✅ Purpose
- ✅ Goal
- ❌ Business Value (needs quantification)
[... continue for all sections]

**Technical Accuracy**: 
- ✅ Rust examples compile
- ❌ Performance targets need revision (5min for 1M LOC unrealistic)
[... continue for all criteria]

**Required Revisions**:
1. Add specific performance benchmarks
2. Include error handling examples
3. Expand anti-patterns section

**Confidence**: 8/10

### Query Intelligence Natural Language PRP
[Repeat structure for each PRP]

## Cross-PRP Consistency Issues
- [ ] Issue 1: Repository Analysis API output format doesn't match Query Intelligence input
- [ ] Issue 2: Pattern Detection confidence scores use different scales

## Validation Command Results
```bash
# Results of running validation commands from PRPs
```

## Recommendations
1. Priority fixes before Phase 3
2. Nice-to-have improvements
3. Clarifications needed

## Phase 3 Readiness Assessment
- [ ] All critical issues resolved
- [ ] Integration points aligned
- [ ] Validation commands verified
- [ ] PRPs ready for execution

## Sign-off Checklist
- [ ] All PRPs meet Context Engineering standards
- [ ] Technical accuracy verified
- [ ] Executability confirmed
- [ ] Ready for Phase 3: YES/NO
```

## Quality Criteria for Validation

Your validation is complete when:
- [ ] Every PRP section has been checked
- [ ] All code examples have been verified
- [ ] External links have been tested
- [ ] Validation commands have been dry-run
- [ ] Cross-PRP consistency verified
- [ ] Specific revision requirements documented
- [ ] Clear go/no-go decision for Phase 3

## Common Issues to Look For

1. **Vague Requirements**: "Make it fast" vs "Response time <100ms"
2. **Missing Integration Details**: How services communicate
3. **Incomplete Error Handling**: Only happy path documented
4. **Unrealistic Targets**: Performance goals not achievable
5. **Missing Dependencies**: External services not specified
6. **Inconsistent Models**: Data structures don't align
7. **Shallow Pseudocode**: Not detailed enough to implement
8. **Generic Anti-Patterns**: Not specific to the feature
9. **Low Confidence Without Mitigation**: No plan to improve
10. **Validation Commands Don't Work**: Can't actually be run

## Deliverable
Create file: `PRPs/validation-report.md`

## Additional Instructions
- Be extremely thorough - these PRPs guide actual implementation
- If a PRP needs revision, be specific about what's missing
- Consider the perspective of an AI agent trying to implement
- Check that PRPs follow the exact structure from examples
- Verify all technical details are accurate for the platform
- Ensure consistency across all PRPs
- Don't pass a PRP that isn't truly ready

Remember: The quality of validation directly impacts Phase 3 success. Be rigorous in your assessment. It's better to require revisions now than to have implementation failures later.