# AI Agent Prompt: Create Query Intelligence Natural Language PRP

## Task Overview
Create a comprehensive Product Requirements Prompt (PRP) for the Query Intelligence Natural Language Interface. This is the second priority feature that enables users to interact with codebases using natural language queries.

## Context
The Query Intelligence service is the AI-powered brain of CCL that processes natural language queries about codebases. It uses Vertex AI/Gemini 2.5 for understanding and generates accurate, context-aware responses. This service depends on the Repository Analysis API for code context.

## Files You MUST Read (In This Order)

1. **`PRPs/services/query-intelligence.md`** - Complete service specification
   - Understand the Python-based architecture
   - Note the AI/ML integration requirements
   - Review the query processing pipeline

2. **`PRPs/ai-ml/gemini-integration.md`** - Gemini 2.5 integration details
   - Understand rate limits and quotas
   - Review context window management
   - Note the prompt engineering patterns

3. **`PRPs/ai-ml/embeddings.md`** - Embedding strategy
   - Understand vector storage approach
   - Review similarity search patterns
   - Note the chunking strategies

4. **`examples/query-intelligence/query_processor.py`** - Example implementation
   - Study the handler chain pattern
   - Understand the confidence scoring
   - Note the async patterns

5. **`PRPs/implementation-guide.md`** - Python-specific patterns
   - Review type hints and dataclass usage
   - Understand the testing approach
   - Note the error handling patterns

6. **Context Engineering References**:
   - `.claude/commands/generate-prp.md` - PRP structure
   - `PRPs/EXAMPLE_multi_agent_prp.md` - Example format

## Research You MUST Conduct

### 1. Vertex AI/Gemini 2.5 Research
Document thoroughly:
- API authentication patterns
- Rate limits by region (especially us-central1)
- Context window limits (2M tokens)
- Streaming response capabilities
- Error codes and retry strategies
- Pricing and quota management

### 2. RAG (Retrieval Augmented Generation) Pipeline
Research and include:
- Vector database options (Pinecone, Weaviate, Vertex AI Vector Search)
- Chunking strategies for code
- Embedding models (text-embedding-004)
- Similarity search algorithms
- Re-ranking strategies
- Context assembly techniques

### 3. Natural Language Understanding for Code
Find information about:
- Code-specific query patterns
- Intent classification for developer queries
- Query expansion techniques
- Ambiguity resolution
- Multi-turn conversation handling

### 4. Caching and Performance
Research:
- Redis caching patterns for AI responses
- Embedding cache strategies
- Query result caching
- Cache invalidation for code changes

## PRP Structure Requirements

Your PRP MUST follow this exact structure:

```markdown
# Query Intelligence Natural Language Interface PRP

## Purpose
[Explain why natural language querying is essential for CCL]

## Goal
[Specific goals for the query intelligence system]

## Business Value
- Enable non-technical stakeholders to understand codebases
- Reduce time to find code patterns from hours to seconds
- Democratize codebase knowledge across teams
- [Additional quantifiable benefits]

## Success Criteria
- [ ] Process queries in <100ms (p95)
- [ ] Achieve >85% accuracy on benchmark queries
- [ ] Handle context up to 2M tokens
- [ ] Support multi-turn conversations
- [ ] Maintain conversation history
- [ ] Provide confidence scores for all responses
- [ ] Support 15+ programming languages
- [ ] Handle 1000+ concurrent users

## Documentation

### Internal References
- Service specification: `PRPs/services/query-intelligence.md`
- Gemini integration: `PRPs/ai-ml/gemini-integration.md`
- Embeddings guide: `PRPs/ai-ml/embeddings.md`
- Example code: `examples/query-intelligence/query_processor.py`

### External Documentation
- Vertex AI Python SDK: [URL]
- Gemini 2.5 API Reference: [URL]
- LangChain documentation: [URL]
- Redis Python client: [URL]
- [Include 10+ more relevant resources]

### Code Examples from Research
```python
# Include actual code examples for:
# - Vertex AI client initialization
# - Embedding generation
# - RAG pipeline
# - Caching patterns
```

## Implementation Blueprint

### Phase 1: Query Processing Pipeline
```python
@dataclass
class QueryPipeline:
    """
    Main pipeline for processing natural language queries
    """
    async def process_query(self, query: str, context: CodeContext) -> QueryResult:
        # Step 1: Query understanding and classification
        intent = await self.classify_intent(query)
        
        # Step 2: Query expansion and reformulation
        expanded_query = await self.expand_query(query, intent)
        
        # Step 3: Context retrieval (RAG)
        relevant_context = await self.retrieve_context(expanded_query, context)
        
        # Step 4: Generate response with Gemini
        response = await self.generate_response(query, relevant_context)
        
        # Step 5: Post-process and validate
        validated_response = await self.validate_response(response)
        
        # Step 6: Calculate confidence score
        confidence = await self.calculate_confidence(query, response, context)
        
        return QueryResult(
            answer=validated_response,
            confidence=confidence,
            references=relevant_context.references,
            metadata=self.build_metadata()
        )
```

### Phase 2: Handler Chain Implementation
[Detailed handler patterns for different query types]

### Phase 3: RAG System Design
[Complete retrieval augmented generation design]

### Phase 4: Caching Layer
[Multi-level caching strategy]

## Data Models

### Input Models
```python
@dataclass
class QueryRequest:
    query: str
    repository_id: str
    session_id: Optional[str]
    conversation_history: Optional[List[Message]]
    filters: Optional[QueryFilters]
    
@dataclass
class QueryFilters:
    file_patterns: Optional[List[str]]
    languages: Optional[List[str]]
    date_range: Optional[DateRange]
    exclude_tests: bool = False
```

### Output Models
[Complete response models with confidence scores and references]

## Query Handler Types

### 1. Definition Queries
```python
class DefinitionHandler(QueryHandler):
    """Handles 'What is X?' or 'Show me the definition of Y' queries"""
    patterns = [
        r"what is (\w+)",
        r"show me the definition of (\w+)",
        r"where is (\w+) defined"
    ]
```

### 2. Implementation Queries
[Handler for "How does X work?" queries]

### 3. Usage Queries
[Handler for "Where is X used?" queries]

### 4. Architecture Queries
[Handler for high-level architecture questions]

[Include all handler types with examples]

## API Endpoints

### POST /api/v1/query
Submit a natural language query
```python
@app.post("/api/v1/query")
async def process_query(request: QueryRequest) -> QueryResponse:
    """
    Process a natural language query about a codebase
    
    Returns:
        QueryResponse with answer, confidence, and references
    """
```

### GET /api/v1/query/{query_id}
Retrieve query results
[Complete specification]

### POST /api/v1/query/stream
Stream query responses
[Streaming endpoint for long responses]

[Include all endpoints with full specs]

## Vertex AI Integration Details

### Client Configuration
```python
vertexai.init(
    project=PROJECT_ID,
    location="us-central1",  # Highest quotas
    credentials=credentials
)

model = GenerativeModel(
    "gemini-2.5-flash-exp",
    generation_config={
        "temperature": 0.1,  # Low for factual responses
        "top_p": 0.95,
        "max_output_tokens": 8192,
    }
)
```

### Rate Limiting Strategy
[Implement circuit breakers and retry logic]

### Context Window Management
[Strategy for handling large codebases]

## Task List
1. [ ] Set up Python project with Poetry
2. [ ] Implement Vertex AI client wrapper
3. [ ] Create query classification system
4. [ ] Build handler chain architecture
5. [ ] Implement RAG pipeline
6. [ ] Set up vector database
7. [ ] Create embedding generation service
8. [ ] Implement caching layer with Redis
9. [ ] Build confidence scoring system
10. [ ] Create API endpoints with FastAPI
11. [ ] Add streaming response support
12. [ ] Implement conversation memory
13. [ ] Create comprehensive test suite
14. [ ] Add performance monitoring
15. [ ] Build query analytics

## Validation Loops

### Code Quality
```bash
# Type checking
mypy query_intelligence/

# Linting
ruff check query_intelligence/
black --check query_intelligence/

# Security
bandit -r query_intelligence/
safety check
```

### Testing
```bash
# Unit tests
pytest tests/unit/ -v --cov=query_intelligence

# Integration tests
pytest tests/integration/ -v

# AI response quality tests
python tests/quality/benchmark_queries.py
```

### Performance
```bash
# Load testing
locust -f tests/load/query_load_test.py

# Response time analysis
python scripts/analyze_query_performance.py
```

## Anti-Patterns to Avoid
- ❌ Sending entire codebase to LLM
- ❌ Not handling rate limits
- ❌ Ignoring confidence scores
- ❌ No query validation
- ❌ Missing context in responses
- ❌ Not caching embeddings
- ❌ Sequential processing
- ❌ No conversation memory
- ❌ Hallucination without references

## Known Gotchas
- Gemini 2.5 rate limits vary by region (us-central1 highest)
- Context window is 2M tokens but optimal is <1M
- Embedding generation can be expensive at scale
- Vector similarity doesn't always mean semantic similarity
- Multi-language code requires special handling
- Streaming responses need careful error handling
- Cache invalidation is complex with code changes

## Performance Optimization
- Implement embedding cache with 7-day TTL
- Use Redis for sub-millisecond cache lookups
- Batch embedding requests
- Pre-compute embeddings for common queries
- Use Gemini Flash for faster responses
- Implement query result streaming
- Parallelize context retrieval

## Confidence Score Calculation
```python
def calculate_confidence(self, query: str, response: str, context: Context) -> float:
    """
    Multi-factor confidence scoring:
    - Query clarity (0-1)
    - Context relevance (0-1)
    - Response coherence (0-1)
    - Reference quality (0-1)
    - Model certainty (0-1)
    """
```

## Example Queries and Expected Responses
[Provide 20+ example queries with expected outputs]

## Confidence Score: X/10
[Honest assessment]
- High confidence: Query classification, API design
- Medium confidence: RAG optimization, multi-turn handling
- Low confidence: Accuracy benchmarks, scale testing
- Mitigation: [Strategies for low confidence areas]
```