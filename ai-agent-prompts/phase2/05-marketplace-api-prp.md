# AI Agent Prompt: Create Marketplace API Foundation PRP

## Task Overview
Create a comprehensive Product Requirements Prompt (PRP) for the Marketplace API Foundation. This feature enables developers to share, discover, and monetize code patterns through a sophisticated commerce platform.

## Context
The Marketplace service is a Go-based high-performance API that handles the commercial aspects of CCL. It allows developers to package and sell their discovered patterns, creates a community around code intelligence, and provides the monetization layer for the platform. Built with enterprise-grade security and scalability.

## Files You MUST Read (In This Order)

1. **`PRPs/services/marketplace.md`** - Complete service specification
   - Understand the Go architecture
   - Note the commerce requirements
   - Review the API design patterns

2. **`PRPs/api/rest-api.md`** - RESTful API patterns
   - Understand endpoint structure
   - Review authentication approach
   - Note pagination patterns

3. **`PRPs/database/spanner-schema.md`** - Database design
   - Understand transaction patterns
   - Review the schema design
   - Note the scalability approach

4. **`examples/marketplace/api_handler.go`** - Example implementation
   - Study the handler patterns
   - Understand middleware usage
   - Note the error handling

5. **`PRPs/implementation-guide.md`** - Go-specific patterns
   - Review project structure
   - Understand testing approach
   - Note performance patterns

6. **`PRPs/api/webhooks.md`** - Webhook patterns
   - Payment notifications
   - Event handling

## Research You MUST Conduct

### 1. Payment Integration (Stripe)
Research thoroughly:
- Stripe Connect for marketplace payments
- Subscription management APIs
- Usage-based billing
- Payment splits and fees
- Webhook security
- PCI compliance requirements
- International payments

### 2. File Storage (Google Cloud Storage)
Document:
- Signed URLs for secure downloads
- Pattern artifact packaging
- Version control for patterns
- CDN integration
- Access control patterns
- Cost optimization

### 3. Search and Discovery
Research:
- Elasticsearch vs Algolia vs CloudSearch
- Faceted search implementation
- Relevance scoring
- Auto-complete patterns
- Search analytics

### 4. API Design Best Practices
Find information about:
- RESTful API versioning
- Rate limiting strategies
- API key management
- OpenAPI documentation
- GraphQL considerations

## PRP Structure Requirements

Your PRP MUST follow this exact structure:

```markdown
# Marketplace API Foundation PRP

## Purpose
[Explain why a pattern marketplace is essential for CCL's success]

## Goal
[Specific goals for the marketplace API]

## Business Value
- Enable $10M+ annual revenue through pattern sales
- Create network effects with 10,000+ developers
- Reduce development time by 60% through pattern reuse
- Build competitive moat through community
- [Additional quantifiable benefits]

## Success Criteria
- [ ] Handle 10,000+ concurrent users
- [ ] Process payments in <2 seconds
- [ ] Search results in <50ms (p95)
- [ ] 99.99% uptime SLA
- [ ] Support 50+ payment methods
- [ ] Scale to 1M+ patterns
- [ ] Handle $1M+ monthly transactions
- [ ] PCI DSS compliant

## Documentation

### Internal References
- Service specification: `PRPs/services/marketplace.md`
- API patterns: `PRPs/api/rest-api.md`
- Database schema: `PRPs/database/spanner-schema.md`
- Example code: `examples/marketplace/api_handler.go`

### External Documentation
- Stripe API Reference: [URL]
- Google Cloud Storage Go: [URL]
- Spanner Go Client: [URL]
- Chi Router Documentation: [URL]
- [Include 15+ more resources]

### Code Examples from Research
```go
// Include examples for:
// - Stripe integration
// - GCS signed URLs
// - Spanner transactions
// - API middleware
// - Search implementation
```

## Implementation Blueprint

### Phase 1: Core API Structure
```go
// Package structure
marketplace/
├── cmd/
│   └── api/
│       └── main.go
├── internal/
│   ├── api/
│   │   ├── handlers/
│   │   ├── middleware/
│   │   └── routes.go
│   ├── domain/
│   │   ├── pattern/
│   │   ├── user/
│   │   └── payment/
│   ├── infrastructure/
│   │   ├── database/
│   │   ├── storage/
│   │   └── payment/
│   └── application/
│       └── services/
├── pkg/
│   ├── errors/
│   └── validation/
└── api/
    └── openapi.yaml

// Main application setup
func main() {
    ctx := context.Background()
    
    // Load configuration
    cfg := config.Load()
    
    // Initialize dependencies
    db := database.NewSpannerClient(ctx, cfg.Spanner)
    storage := storage.NewGCSClient(ctx, cfg.Storage)
    payment := payment.NewStripeClient(cfg.Stripe)
    search := search.NewClient(cfg.Search)
    
    // Create services
    patternService := services.NewPatternService(db, storage)
    paymentService := services.NewPaymentService(payment, db)
    searchService := services.NewSearchService(search, db)
    
    // Setup API
    router := api.NewRouter(
        patternService,
        paymentService,
        searchService,
    )
    
    // Start server
    server := &http.Server{
        Addr:    cfg.ServerAddr,
        Handler: router,
    }
    
    log.Fatal(server.ListenAndServe())
}
```

### Phase 2: Pattern Management
```go
type PatternService struct {
    db      *spanner.Client
    storage *storage.Client
}

func (s *PatternService) CreatePattern(ctx context.Context, req CreatePatternRequest) (*Pattern, error) {
    // Validate pattern
    if err := s.validatePattern(req); err != nil {
        return nil, errors.Wrap(err, "invalid pattern")
    }
    
    // Upload pattern artifacts
    artifactURL, err := s.uploadArtifacts(ctx, req.Files)
    if err != nil {
        return nil, errors.Wrap(err, "upload failed")
    }
    
    // Create database record
    pattern := &Pattern{
        ID:          uuid.New().String(),
        Name:        req.Name,
        Description: req.Description,
        Price:       req.Price,
        ArtifactURL: artifactURL,
        AuthorID:    req.AuthorID,
        Status:      PatternStatusPending,
    }
    
    // Start transaction
    _, err = s.db.ReadWriteTransaction(ctx, func(ctx context.Context, txn *spanner.ReadWriteTransaction) error {
        // Insert pattern
        // Update search index
        // Create audit log
        return nil
    })
    
    return pattern, err
}
```

### Phase 3: Payment Integration
[Detailed Stripe Connect implementation]

### Phase 4: Search Implementation
[Full search service design]

## Data Models

### Domain Models
```go
type Pattern struct {
    ID              string
    Name            string
    Description     string
    Category        PatternCategory
    Languages       []string
    Price           Money
    AuthorID        string
    ArtifactURL     string
    ThumbnailURL    string
    Documentation   string
    Stats           PatternStats
    Reviews         []Review
    CreatedAt       time.Time
    UpdatedAt       time.Time
    Status          PatternStatus
}

type Money struct {
    Amount   int64  // In cents
    Currency string // ISO 4217
}

type Purchase struct {
    ID            string
    PatternID     string
    BuyerID       string
    Price         Money
    PaymentID     string // Stripe payment intent
    Status        PurchaseStatus
    DownloadCount int
    CreatedAt     time.Time
}

type User struct {
    ID               string
    Email            string
    StripeCustomerID string
    StripeAccountID  string // For sellers
    Patterns         []string
    Purchases        []string
    Balance          Money
    Settings         UserSettings
}
```

### API Models
[Request/Response DTOs]

## API Endpoints

### Pattern Management
```go
// List patterns with filtering
GET /api/v1/patterns
Query params:
- category: string
- language: string
- price_min: int
- price_max: int
- sort: popularity|price|date
- page: int
- limit: int

// Get pattern details
GET /api/v1/patterns/{id}

// Create new pattern (authenticated)
POST /api/v1/patterns
Body: {
    name: string
    description: string
    category: string
    languages: []string
    price: {amount: int, currency: string}
    files: multipart
}

// Update pattern
PUT /api/v1/patterns/{id}

// Delete pattern
DELETE /api/v1/patterns/{id}
```

### Purchase Flow
```go
// Purchase pattern
POST /api/v1/purchases
Body: {
    pattern_id: string
    payment_method: string
}

// Get purchase history
GET /api/v1/users/{id}/purchases

// Download pattern
GET /api/v1/purchases/{id}/download
Returns: Signed URL for pattern download
```

### Search Endpoints
[Complete search API specification]

### Webhook Endpoints
[Stripe webhook handlers]

## Payment Integration Details

### Stripe Connect Setup
```go
// Onboard pattern authors
func (s *PaymentService) OnboardAuthor(ctx context.Context, userID string) (string, error) {
    // Create connected account
    params := &stripe.AccountParams{
        Type: stripe.String("express"),
        Capabilities: &stripe.AccountCapabilitiesParams{
            CardPayments: &stripe.AccountCapabilitiesCardPaymentsParams{
                Requested: stripe.Bool(true),
            },
            Transfers: &stripe.AccountCapabilitiesTransfersParams{
                Requested: stripe.Bool(true),
            },
        },
    }
    
    account, err := account.New(params)
    if err != nil {
        return "", err
    }
    
    // Create onboarding link
    linkParams := &stripe.AccountLinkParams{
        Account:    stripe.String(account.ID),
        RefreshURL: stripe.String(s.cfg.RefreshURL),
        ReturnURL:  stripe.String(s.cfg.ReturnURL),
        Type:       stripe.String("account_onboarding"),
    }
    
    link, err := accountlink.New(linkParams)
    return link.URL, err
}
```

### Payment Processing
[Complete payment flow with splits]

## Task List
1. [ ] Set up Go project with standard layout
2. [ ] Implement Spanner database layer
3. [ ] Create domain models and validation
4. [ ] Build RESTful API routes with Chi
5. [ ] Implement authentication middleware
6. [ ] Integrate Stripe Connect
7. [ ] Create pattern upload system
8. [ ] Implement search service
9. [ ] Build purchase flow
10. [ ] Add download management
11. [ ] Create webhook handlers
12. [ ] Implement rate limiting
13. [ ] Add comprehensive logging
14. [ ] Create admin endpoints
15. [ ] Build analytics system
16. [ ] Add API documentation
17. [ ] Implement caching layer
18. [ ] Create integration tests

## Validation Loops

### Code Quality
```bash
# Go linting
golangci-lint run ./...

# Format check
gofmt -l .

# Security scan
gosec ./...

# Dependency check
go mod verify
nancy go.sum
```

### Testing
```bash
# Unit tests
go test ./... -v -cover

# Integration tests
go test ./tests/integration -tags=integration

# Load tests
go run ./tests/load/main.go

# Contract tests
go test ./tests/contract
```

### API Testing
```bash
# OpenAPI validation
openapi-spec-validator api/openapi.yaml

# API contract tests
dredd api/openapi.yaml http://localhost:8080
```

## Anti-Patterns to Avoid
- ❌ Storing payment data directly
- ❌ Not using idempotency keys
- ❌ Synchronous payment processing
- ❌ Missing transaction boundaries
- ❌ Not handling webhook retries
- ❌ Exposing internal IDs
- ❌ Not versioning APIs
- ❌ Missing rate limiting
- ❌ Not validating webhooks

## Known Gotchas
- Spanner has 20,000 mutation limit per transaction
- Stripe webhooks can arrive out of order
- GCS signed URLs expire (plan for refresh)
- Search index updates are eventually consistent
- Payment regulations vary by country
- File upload size limits need enforcement
- Currency conversion complexities

## Performance Optimization
- Use Spanner read replicas for queries
- Implement Redis caching for hot data
- Use CDN for pattern artifacts
- Batch database operations
- Implement connection pooling
- Use pagination for all list endpoints
- Optimize search queries with filters
- Pre-compute pattern statistics

## Security Considerations
```go
// API key validation
func ValidateAPIKey(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        key := r.Header.Get("X-API-Key")
        
        // Validate against database
        user, err := validateKey(r.Context(), key)
        if err != nil {
            http.Error(w, "Unauthorized", http.StatusUnauthorized)
            return
        }
        
        // Add user to context
        ctx := context.WithValue(r.Context(), "user", user)
        next.ServeHTTP(w, r.WithContext(ctx))
    })
}
```

## Monitoring and Analytics
- Track pattern views/downloads
- Monitor conversion rates
- Analyze search queries
- Track API performance
- Monitor payment success rates
- Alert on anomalies

## Confidence Score: X/10
[Honest assessment]
- High confidence: API design, Go patterns, database design
- Medium confidence: Search implementation, international payments
- Low confidence: Scale testing, marketplace dynamics
- Mitigation: [Improvement strategies]
```

## Specific Requirements for This PRP

### Must Include:
1. **Complete API specification** with OpenAPI schema
2. **Stripe Connect integration** with code examples
3. **Search implementation** with performance targets
4. **File storage strategy** with security
5. **Database transaction patterns** for Spanner
6. **Rate limiting approach** with tiers
7. **Webhook handling** with retry logic
8. **Pricing models** (fixed, subscription, usage-based)

### Critical Design Decisions:
- API versioning strategy
- Authentication method (JWT vs API keys)
- Search technology choice
- Caching strategy
- File storage structure
- Payment split calculations
- Fraud prevention measures

### Marketplace Features:
- Pattern reviews and ratings
- Author profiles and statistics
- Pattern versioning
- License management
- Refund policies
- Pattern collections/bundles
- Affiliate program

## Quality Criteria

Your PRP will be considered complete when:
- [ ] All API endpoints are specified
- [ ] Payment flow is comprehensive
- [ ] Search implementation is detailed
- [ ] Security measures are robust
- [ ] Performance targets are clear
- [ ] Database design is complete
- [ ] Error handling is specified
- [ ] Monitoring strategy is defined
- [ ] Integration points are clear
- [ ] Scalability plan is included

## Deliverable
Create file: `PRPs/features/marketplace-api-foundation.md`

## Additional Notes
- Consider international tax implications
- Plan for pattern quality control
- Design for multi-tenancy
- Include A/B testing capabilities
- Consider recommendation engine
- Plan for dispute resolution
- Design for accessibility compliance

Remember: The marketplace is the monetization engine for CCL. It must be secure, scalable, and provide an excellent developer experience. Focus on building trust through robust payment handling and quality control.