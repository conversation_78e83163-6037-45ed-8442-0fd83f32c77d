# AI Agent Prompt: Feature Inventory and Prioritization

## Task Overview
You need to create a comprehensive feature inventory from the CCL project and prioritize them for PRP creation. This is Step 1 of finalizing Phase 2 of the Context Engineering workflow.

## Context
The CCL (Codebase Context Layer) project is following the Context Engineering methodology for AI-assisted development. Currently, the project has completed Phase 1 (setup) and is in Phase 2 (PRP Generation). Your task is to extract all features that need PRPs and create a prioritized list.

## Files You MUST Read (In This Order)

1. **`TASK.md`** - Contains the current task list with status percentages
   - Look for sections: "In Progress", "Ready to Start", "Backlog"
   - Note the completion percentages for each task
   - Pay attention to task dependencies mentioned

2. **`PLANNING.md`** - Contains overall project goals and architecture
   - Review the "Core Features" section
   - Understand the technical architecture
   - Note the success criteria and metrics

3. **`PRPs/feature-specifications.md`** - Contains detailed feature requirements
   - Cross-reference with tasks in TASK.md
   - Identify which features have partial documentation

4. **`finalize-phase2-prp-plan.md`** - Contains the overall plan for Phase 2
   - Understand the expected deliverables
   - Review the prioritization criteria

## Specific Instructions

### Step 1: Extract Features
Create a comprehensive list of ALL features mentioned across the files:
- Features currently "In Progress" with their completion percentage
- Features marked as "Ready to Start"
- Core features from PLANNING.md that aren't yet in TASK.md
- Any features mentioned in feature-specifications.md

### Step 2: Analyze Dependencies
For each feature, identify:
- Technical dependencies (what must be built first)
- Service dependencies (which microservice owns it)
- Data dependencies (what schemas/APIs it needs)

### Step 3: Prioritization Criteria
Apply these criteria in order:
1. **Current Progress** - Features already started (60% complete) go first
2. **No Dependencies** - Features that can be built independently
3. **Business Value** - Features critical to MVP (from PLANNING.md)
4. **Technical Foundation** - Features other features depend on
5. **Complexity** - Simpler features before complex ones

### Step 4: Create Prioritized List
Structure the output as follows:
```markdown
# CCL Feature Priority List for PRP Generation

## Priority 1: In-Progress Features
1. **Repository Analysis API** (60% complete)
   - Service: analysis-engine
   - Dependencies: None
   - Why Priority 1: Already 60% complete, no blockers

## Priority 2: Foundation Features
2. **Query Intelligence Natural Language Interface**
   - Service: query-intelligence
   - Dependencies: Repository Analysis API (for code context)
   - Why Priority 2: Core MVP feature, enables user interaction

[Continue for all features...]

## Feature Dependency Map
```mermaid
graph TD
    A[Repository Analysis API] --> B[Query Intelligence]
    A --> C[Pattern Detection]
    B --> D[SDK Development]
    C --> E[Marketplace API]
```

## Implementation Order Rationale
[Explain why this order makes sense]
```

## Deliverable
Create file: `PRPs/feature-priority-list.md`

## Quality Criteria
Your prioritized list MUST:
- Include EVERY feature from TASK.md and PLANNING.md
- Show clear dependencies between features
- Justify each priority decision
- Include a visual dependency map
- Separate features by their readiness (in-progress vs ready-to-start vs blocked)
- Indicate which microservice owns each feature

## Expected Features to Find (Minimum)
Based on the project documentation, you should find at least:
- Repository Analysis API (analysis-engine)
- Query Intelligence Natural Language Interface (query-intelligence)
- Pattern Detection MVP (pattern-mining)
- Marketplace API Foundation (marketplace)
- Authentication System (shared)
- Real-time Collaboration (collaboration)
- SDK Development (sdk)
- GraphQL API Layer (web)
- WebSocket Support (collaboration)

## Validation
After creating the list, verify:
- [ ] All features from TASK.md are included
- [ ] All core features from PLANNING.md are included
- [ ] Dependencies make logical sense
- [ ] Priority order follows the criteria
- [ ] Each feature has a clear owner service
- [ ] The implementation order is feasible

## Additional Notes
- If you find features mentioned in one document but not others, include them with a note
- If completion percentages seem incorrect based on the evidence, note the discrepancy
- If you identify missing features that seem critical, add them to a "Recommended Additions" section
- Consider both technical and business perspectives in prioritization

Remember: This list will guide the creation of all feature PRPs, so accuracy and completeness are critical.