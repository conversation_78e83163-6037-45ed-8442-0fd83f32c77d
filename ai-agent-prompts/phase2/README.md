# AI Agent Prompts for Phase 2 Finalization

This directory contains extremely detailed prompts for AI agents to execute each step of the CCL Phase 2 finalization plan. Each prompt is designed to be self-contained with all necessary context, file references, and expected outputs.

## Prompt Files

1. **`01-feature-inventory-prioritization.md`** - Extract and prioritize features from TASK.md
2. **`02-repository-analysis-api-prp.md`** - Create Repository Analysis API PRP
3. **`03-query-intelligence-prp.md`** - Create Query Intelligence Natural Language PRP
4. **`04-pattern-detection-prp.md`** - Create Pattern Detection MVP PRP
5. **`05-marketplace-api-prp.md`** - Create Marketplace API Foundation PRP
6. **`06-validation-checklist.md`** - Validate all PRPs for completeness
7. **`07-completion-report.md`** - Create Phase 2 completion report

## Usage Instructions

1. Give each prompt to an AI agent in sequence
2. Ensure the agent has access to the CCL project repository
3. Each prompt includes:
   - Specific context about the task
   - Exact files to reference
   - Expected deliverables
   - Quality criteria
   - Examples where applicable

## Important Notes

- Each prompt assumes the AI agent has full access to the episteme repository
- Prompts include validation steps to ensure quality
- Expected output locations are specified in each prompt
- Follow the numerical order for best results