# AI Agent Prompt: Create Pattern Detection MVP PRP

## Task Overview
Create a comprehensive Product Requirements Prompt (PRP) for the Pattern Detection MVP feature. This feature uses machine learning to automatically identify architectural patterns, code smells, and best practices within codebases.

## Context
The Pattern Detection service is a Python-based ML system that mines codebases for recurring patterns. It's a key differentiator for CCL, enabling automated discovery of design patterns, anti-patterns, security vulnerabilities, and performance bottlenecks. This service processes the AST data from the Repository Analysis API.

## Files You MUST Read (In This Order)

1. **`PRPs/services/pattern-mining.md`** - Complete service specification
   - Understand the ML pipeline architecture
   - Note the pattern types to detect
   - Review performance requirements

2. **`PRPs/ai-ml/pattern-recognition.md`** - ML approach details
   - Understand clustering algorithms
   - Review feature extraction methods
   - Note the pattern scoring system

3. **`PRPs/database/bigquery-analytics.md`** - Data storage patterns
   - Understand how patterns are stored
   - Review aggregation strategies
   - Note the query patterns

4. **`examples/pattern-mining/`** - Example implementations
   - Study any example code available
   - Understand the data flow
   - Note the ML model structure

5. **`PRPs/implementation-guide.md`** - Python ML patterns
   - Review scikit-learn usage
   - Understand the async patterns
   - Note the testing approaches

## Research You MUST Conduct

### 1. ML Algorithms for Code Pattern Detection
Research thoroughly:
- DBSCAN for clustering code patterns
- K-means for pattern grouping
- Autoencoders for pattern extraction
- Graph neural networks for AST analysis
- Similarity metrics for code comparison

### 2. Feature Engineering for Code
Document:
- AST-based features (node types, depth, complexity)
- Lexical features (naming patterns, comments)
- Structural features (coupling, cohesion)
- Semantic features (data flow, control flow)
- Statistical features (LOC, cyclomatic complexity)

### 3. BigQuery ML Integration
Research:
- BQML capabilities and limitations
- Model training on large datasets
- Batch prediction patterns
- Cost optimization strategies
- Real-time vs batch processing

### 4. Pattern Types to Detect
Find examples of:
- Design patterns (Singleton, Factory, Observer, etc.)
- Architecture patterns (MVC, Repository, CQRS)
- Anti-patterns (God Class, Spaghetti Code)
- Security patterns (Input Validation, Authentication)
- Performance patterns (Caching, Lazy Loading)

## PRP Structure Requirements

Your PRP MUST follow this exact structure:

```markdown
# Pattern Detection MVP PRP

## Purpose
[Explain why automated pattern detection is crucial for CCL]

## Goal
[Specific, measurable goals for pattern detection]

## Business Value
- Reduce code review time by 40% through automated pattern identification
- Prevent security vulnerabilities before production
- Standardize coding practices across teams
- Enable pattern marketplace monetization
- [Additional quantifiable benefits]

## Success Criteria
- [ ] Detect 20+ common design patterns with >90% accuracy
- [ ] Process 1M LOC in <30 seconds
- [ ] Identify security vulnerabilities with <5% false positives
- [ ] Support 10+ programming languages
- [ ] Generate pattern confidence scores
- [ ] Provide actionable recommendations
- [ ] Scale to 1000+ concurrent analyses

## Documentation

### Internal References
- Service specification: `PRPs/services/pattern-mining.md`
- Pattern recognition: `PRPs/ai-ml/pattern-recognition.md`
- BigQuery integration: `PRPs/database/bigquery-analytics.md`
- Implementation guide: `PRPs/implementation-guide.md`

### External Documentation
- Scikit-learn clustering: [URL]
- BigQuery ML documentation: [URL]
- AST analysis papers: [URLs]
- Code2Vec research: [URL]
- [Include 10+ more ML/pattern detection resources]

### Research Papers
- "Machine Learning for Code Analysis": [Citation]
- "Detecting Design Patterns with Deep Learning": [Citation]
- [Include 5+ relevant papers]

## Implementation Blueprint

### Phase 1: Feature Extraction Pipeline
```python
class FeatureExtractor:
    """Extract ML features from AST and code metrics"""
    
    def extract_features(self, ast_data: ASTNode) -> np.ndarray:
        features = []
        
        # Structural features
        features.extend(self.extract_structural_features(ast_data))
        
        # Lexical features
        features.extend(self.extract_lexical_features(ast_data))
        
        # Semantic features
        features.extend(self.extract_semantic_features(ast_data))
        
        # Statistical features
        features.extend(self.extract_statistical_features(ast_data))
        
        return np.array(features)
    
    def extract_structural_features(self, ast: ASTNode) -> List[float]:
        """
        - AST depth
        - Node type distribution
        - Branch complexity
        - Nesting levels
        """
        
    def extract_lexical_features(self, ast: ASTNode) -> List[float]:
        """
        - Identifier patterns
        - Naming conventions
        - Comment density
        - Code style metrics
        """
```

### Phase 2: Pattern Detection Models
```python
class PatternDetector:
    """Multi-model ensemble for pattern detection"""
    
    def __init__(self):
        self.design_pattern_model = self.load_design_pattern_model()
        self.antipattern_model = self.load_antipattern_model()
        self.security_model = self.load_security_model()
        self.performance_model = self.load_performance_model()
    
    async def detect_patterns(self, features: np.ndarray) -> List[Pattern]:
        # Run models in parallel
        results = await asyncio.gather(
            self.detect_design_patterns(features),
            self.detect_antipatterns(features),
            self.detect_security_issues(features),
            self.detect_performance_issues(features)
        )
        
        return self.merge_and_rank_patterns(results)
```

### Phase 3: BigQuery ML Integration
[Detailed BQML pipeline design]

### Phase 4: Pattern Marketplace Integration
[How detected patterns become marketplace items]

## Data Models

### Input Models
```python
@dataclass
class PatternDetectionRequest:
    repository_id: str
    ast_data: Dict[str, Any]  # From Repository Analysis API
    detection_config: DetectionConfig
    
@dataclass
class DetectionConfig:
    pattern_types: List[PatternType]
    confidence_threshold: float = 0.7
    language_hints: List[str]
    exclude_patterns: List[str]
```

### Pattern Models
```python
@dataclass
class DetectedPattern:
    id: str
    pattern_type: PatternType
    pattern_name: str
    confidence: float
    locations: List[CodeLocation]
    description: str
    recommendation: str
    severity: Severity
    examples: List[CodeExample]
    
class PatternType(Enum):
    DESIGN_PATTERN = "design_pattern"
    ANTI_PATTERN = "anti_pattern"
    SECURITY_VULNERABILITY = "security"
    PERFORMANCE_ISSUE = "performance"
    CODE_SMELL = "code_smell"
```

## ML Pipeline Architecture

### Training Pipeline
```python
class PatternTrainingPipeline:
    """
    Offline training pipeline for pattern models
    """
    def train_models(self, labeled_data: pd.DataFrame):
        # Step 1: Feature engineering
        features = self.engineer_features(labeled_data)
        
        # Step 2: Model selection
        models = {
            'dbscan': DBSCAN(eps=0.3, min_samples=5),
            'kmeans': KMeans(n_clusters=50),
            'autoencoder': self.build_autoencoder(),
            'random_forest': RandomForestClassifier()
        }
        
        # Step 3: Train ensemble
        ensemble = self.train_ensemble(models, features)
        
        # Step 4: Export to BigQuery ML
        self.export_to_bqml(ensemble)
```

### Inference Pipeline
[Real-time pattern detection flow]

## Pattern Detection Algorithms

### Design Pattern Detection
```python
def detect_singleton_pattern(ast: ASTNode) -> Optional[Pattern]:
    """
    Detect Singleton pattern:
    - Private constructor
    - Static instance variable
    - Public getInstance method
    """
    indicators = {
        'private_constructor': False,
        'static_instance': False,
        'get_instance_method': False
    }
    
    # Check for pattern indicators
    # Return pattern with confidence score
```

[Include algorithms for 10+ patterns]

## Task List
1. [ ] Set up Python ML project structure
2. [ ] Implement feature extraction pipeline
3. [ ] Create pattern labeling tools
4. [ ] Build training data collection system
5. [ ] Implement DBSCAN clustering model
6. [ ] Create pattern classification models
7. [ ] Build ensemble model system
8. [ ] Integrate with BigQuery ML
9. [ ] Create real-time inference API
10. [ ] Implement pattern confidence scoring
11. [ ] Build pattern visualization tools
12. [ ] Create comprehensive test suite
13. [ ] Add pattern explanation system
14. [ ] Implement feedback loop for model improvement
15. [ ] Create pattern marketplace integration

## Validation Loops

### Model Quality
```bash
# Run model evaluation
python -m pattern_mining.evaluate --model ensemble --dataset test

# Check pattern detection accuracy
python scripts/pattern_accuracy_test.py

# Validate feature extraction
python -m pytest tests/features/
```

### Performance Testing
```bash
# Benchmark feature extraction
python benchmarks/feature_extraction_bench.py

# Test inference latency
python benchmarks/inference_latency_test.py

# BigQuery cost analysis
bq query --use_legacy_sql=false < queries/cost_analysis.sql
```

### Pattern Quality
```bash
# Manual pattern review
python tools/pattern_review_ui.py

# False positive analysis
python scripts/analyze_false_positives.py
```

## Anti-Patterns to Avoid
- ❌ Training on biased datasets
- ❌ Ignoring language-specific patterns
- ❌ Over-fitting to specific codebases
- ❌ Not handling code variations
- ❌ Missing confidence thresholds
- ❌ Sequential processing of patterns
- ❌ Ignoring pattern context
- ❌ Not validating with developers

## Known Gotchas
- DBSCAN epsilon tuning is critical for pattern quality
- Feature dimensions must be consistent across languages
- BigQuery ML has model size limitations
- Pattern detection accuracy varies by language
- Some patterns are subjective (require human validation)
- Training data quality directly impacts results
- Memory usage scales with codebase size

## Performance Optimization
- Use incremental learning for model updates
- Implement feature caching with Redis
- Batch predictions for efficiency
- Use BigQuery clustering for faster queries
- Parallelize feature extraction
- Implement early stopping for low-confidence patterns
- Use approximate algorithms for large codebases

## Pattern Confidence Scoring
```python
def calculate_pattern_confidence(
    pattern: Pattern,
    features: np.ndarray,
    model_predictions: List[float]
) -> float:
    """
    Multi-factor confidence calculation:
    - Model confidence (0-1)
    - Feature strength (0-1)
    - Pattern completeness (0-1)
    - Cross-validation score (0-1)
    - Historical accuracy (0-1)
    """
```

## Example Patterns and Detection Logic
[Provide 20+ patterns with detection algorithms]

### Example: Repository Pattern Detection
```python
def detect_repository_pattern(ast: ASTNode) -> Optional[Pattern]:
    """
    Repository Pattern indicators:
    - Interface with data access methods
    - Concrete implementation
    - Separation of concerns
    - CRUD operations
    """
    # Detailed detection logic
```

## Integration with Marketplace
- How detected patterns become sellable items
- Pattern packaging format
- Quality requirements for marketplace
- Pricing model for patterns

## Confidence Score: X/10
[Honest assessment]
- High confidence: Feature extraction, clustering algorithms
- Medium confidence: Pattern accuracy targets, BigQuery ML scale
- Low confidence: Real-time performance at scale
- Mitigation: [Strategies for improvement]
```

## Specific Requirements for This PRP

### Must Include:
1. **Complete ML pipeline** from feature extraction to prediction
2. **20+ pattern detection algorithms** with pseudocode
3. **Feature engineering details** for code analysis
4. **BigQuery ML integration** with cost estimates
5. **Confidence scoring system** with multiple factors
6. **Training data strategy** and labeling approach
7. **Performance benchmarks** for each component
8. **Pattern visualization** approach

### Critical Algorithms:
- DBSCAN implementation for pattern clustering
- Feature extraction from AST
- Ensemble model architecture
- Confidence score calculation
- Pattern ranking algorithm
- Feedback loop design

### Data Requirements:
- Training data sources
- Labeling strategy
- Data augmentation techniques
- Validation dataset creation
- Continuous learning approach

## Quality Criteria

Your PRP will be considered complete when:
- [ ] All ML algorithms are specified
- [ ] Feature extraction is comprehensive
- [ ] Pattern types are well-defined
- [ ] Training pipeline is complete
- [ ] Inference optimization is detailed
- [ ] BigQuery integration is specified
- [ ] Performance targets are realistic
- [ ] Confidence scoring is robust
- [ ] Integration points are clear
- [ ] Validation strategy is comprehensive

## Deliverable
Create file: `PRPs/features/pattern-detection-mvp.md`

## Additional Notes
- Consider privacy when storing code patterns
- Plan for pattern evolution over time
- Design for explainable AI (why was this pattern detected?)
- Include pattern remediation suggestions
- Consider cultural/team differences in patterns
- Plan for pattern combination detection

Remember: Pattern detection is a key differentiator for CCL. The quality and accuracy of pattern detection directly impacts the value proposition. Focus on practical, actionable patterns that developers actually care about.